"""
搜索服务 - 集成现有的搜索调研功能
"""
import os
import sys
import threading
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.core.search_research import SearchResearchClass
from web_app.models.ai_source import AISource
from web_app.models.data_source import DataSource
from web_app.utils.websocket import create_task_logger, LogLevel


class WebSearchService:
    """Web搜索服务类"""
    
    def __init__(self):
        self.active_tasks = {}
        self.task_results = {}
    
    def start_search_task(self, user_id, ai_source_id, data_source_id, company_name, options=None):
        """
        启动搜索任务
        
        Args:
            user_id: 用户ID
            ai_source_id: AI源ID
            data_source_id: 数据源ID
            company_name: 公司名称
            options: 搜索选项
            
        Returns:
            tuple: (success, message, task_id)
        """
        try:
            # 验证AI源和数据源
            ai_source = AISource.query.filter_by(id=ai_source_id, enabled=True).first()
            if not ai_source:
                return False, "AI源不存在或已禁用", None
            
            data_source = DataSource.query.filter_by(id=data_source_id, enabled=True).first()
            if not data_source:
                return False, "数据源不存在或已禁用", None
            
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建任务信息
            task_info = {
                'task_id': task_id,
                'user_id': user_id,
                'ai_source': ai_source.to_dict(),
                'data_source': data_source.to_dict(),
                'company_name': company_name,
                'options': options or {},
                'status': 'started',
                'created_at': datetime.utcnow(),
                'started_at': None,
                'completed_at': None,
                'result': None,
                'error': None
            }
            
            self.active_tasks[task_id] = task_info
            
            # 启动后台搜索线程
            search_thread = threading.Thread(
                target=self._execute_search,
                args=(task_id, ai_source, data_source, company_name, options)
            )
            search_thread.daemon = True
            search_thread.start()
            
            return True, "搜索任务已启动", task_id
            
        except Exception as e:
            return False, f"启动搜索任务失败: {str(e)}", None
    
    def _execute_search(self, task_id, ai_source, data_source, company_name, options):
        """
        执行搜索任务（后台线程）
        
        Args:
            task_id: 任务ID
            ai_source: AI源对象
            data_source: 数据源对象
            company_name: 公司名称
            options: 搜索选项
        """
        task_logger = create_task_logger(task_id)
        
        try:
            # 更新任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]['status'] = 'running'
                self.active_tasks[task_id]['started_at'] = datetime.utcnow()
            
            # 创建搜索节点结构
            task_logger.start_node("root")
            task_logger.log(LogLevel.INFO, f"开始搜索任务: {company_name}")
            
            # 初始化节点
            init_node = task_logger.create_node("init", "初始化", "配置搜索环境和参数")
            task_logger.start_node("init")
            task_logger.log(LogLevel.INFO, f"使用AI源: {ai_source.name}")
            task_logger.log(LogLevel.INFO, f"使用数据源: {data_source.name}")
            
            # 配置环境变量（从AI源和数据源配置中获取）
            self._configure_environment(ai_source, data_source, task_logger)
            task_logger.complete_node("init")
            
            # 创建搜索研究实例
            search_node = task_logger.create_node("search", "执行搜索", "使用SearchResearchClass进行公司调研")
            task_logger.start_node("search")
            
            try:
                # 实例化搜索研究类
                researcher = SearchResearchClass()
                task_logger.log(LogLevel.INFO, "SearchResearchClass 实例化成功")
                
                # 执行搜索
                task_logger.log(LogLevel.INFO, f"开始调研公司: {company_name}")
                result = researcher.research_company(company_name)
                
                task_logger.log(LogLevel.SUCCESS, "搜索调研完成")
                task_logger.complete_node("search")
                
                # 处理结果
                result_node = task_logger.create_node("result", "处理结果", "清理和格式化搜索结果")
                task_logger.start_node("result")
                
                # 清理结果（移除不需要的字段）
                cleaned_result = self._clean_result(result)
                task_logger.log(LogLevel.INFO, f"结果清理完成，包含 {len(cleaned_result.get('investor_relations_urls', []))} 个投资者关系页面")
                
                task_logger.complete_node("result")
                task_logger.complete_node("root")
                
                # 保存结果
                if task_id in self.active_tasks:
                    self.active_tasks[task_id]['status'] = 'completed'
                    self.active_tasks[task_id]['completed_at'] = datetime.utcnow()
                    self.active_tasks[task_id]['result'] = cleaned_result
                
                self.task_results[task_id] = cleaned_result
                task_logger.log(LogLevel.SUCCESS, "任务执行成功完成")
                
            except Exception as search_error:
                task_logger.fail_node("search", f"搜索执行失败: {str(search_error)}")
                raise search_error
                
        except Exception as e:
            error_message = f"任务执行失败: {str(e)}"
            task_logger.log(LogLevel.ERROR, error_message)
            task_logger.fail_node("root", error_message)
            
            # 更新任务状态
            if task_id in self.active_tasks:
                self.active_tasks[task_id]['status'] = 'failed'
                self.active_tasks[task_id]['completed_at'] = datetime.utcnow()
                self.active_tasks[task_id]['error'] = error_message
    
    def _configure_environment(self, ai_source, data_source, task_logger):
        """配置环境变量"""
        try:
            # 配置AI源相关环境变量
            ai_config = ai_source.get_config_params()
            if ai_source.api_key:
                os.environ['OPENAI_API_KEY'] = ai_source.api_key
                task_logger.log(LogLevel.INFO, "已配置OpenAI API密钥")
            
            if ai_source.endpoint_url:
                os.environ['OPENAI_BASE_URL'] = ai_source.endpoint_url
                task_logger.log(LogLevel.INFO, f"已配置OpenAI端点: {ai_source.endpoint_url}")
            
            if ai_source.model_name:
                os.environ['OPENAI_MODEL'] = ai_source.model_name
                task_logger.log(LogLevel.INFO, f"已配置模型: {ai_source.model_name}")
            
            # 配置数据源相关环境变量
            data_config = data_source.get_config_params()
            if data_source.source_type == 'google_search':
                if data_config.get('api_url'):
                    os.environ['GOOGLE_SEARCH_API_URL'] = data_config['api_url']
                    task_logger.log(LogLevel.INFO, "已配置Google搜索API")
            
            task_logger.log(LogLevel.SUCCESS, "环境配置完成")
            
        except Exception as e:
            task_logger.log(LogLevel.WARNING, f"环境配置警告: {str(e)}")
    
    def _clean_result(self, result):
        """清理搜索结果，移除不需要的字段"""
        if not result:
            return {}
        
        # 移除指定字段
        fields_to_remove = [
            'news_xpath_rules', 
            'investor_xpath_rules', 
            'general_xpath_rules', 
            'all_xpath_rules'
        ]
        
        cleaned_result = result.copy()
        for field in fields_to_remove:
            cleaned_result.pop(field, None)
        
        return cleaned_result
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        return None
    
    def get_task_result(self, task_id):
        """获取任务结果"""
        if task_id in self.task_results:
            return self.task_results[task_id]
        return None
    
    def cancel_task(self, task_id):
        """取消任务"""
        if task_id in self.active_tasks:
            self.active_tasks[task_id]['status'] = 'cancelled'
            self.active_tasks[task_id]['completed_at'] = datetime.utcnow()
            return True
        return False
    
    def get_user_tasks(self, user_id, limit=50):
        """获取用户的任务列表"""
        user_tasks = []
        for task_id, task_info in self.active_tasks.items():
            if task_info['user_id'] == user_id:
                user_tasks.append(task_info)
        
        # 按创建时间倒序排列
        user_tasks.sort(key=lambda x: x['created_at'], reverse=True)
        return user_tasks[:limit]


# 全局搜索服务实例
search_service = WebSearchService()
